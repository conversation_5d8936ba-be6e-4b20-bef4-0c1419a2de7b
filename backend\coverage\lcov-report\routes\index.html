
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for routes</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> routes</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">58.77% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>67/114</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">58.33% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>14/24</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">62.5% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>5/8</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">58.77% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>67/114</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="dev.js"><a href="dev.js.html">dev.js</a></td>
	<td data-value="15.78" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 15%"></div><div class="cover-empty" style="width: 85%"></div></div>
	</td>
	<td data-value="15.78" class="pct low">15.78%</td>
	<td data-value="38" class="abs low">6/38</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="15.78" class="pct low">15.78%</td>
	<td data-value="38" class="abs low">6/38</td>
	</tr>

<tr>
	<td class="file medium" data-value="health.js"><a href="health.js.html">health.js</a></td>
	<td data-value="70.58" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 70%"></div><div class="cover-empty" style="width: 30%"></div></div>
	</td>
	<td data-value="70.58" class="pct medium">70.58%</td>
	<td data-value="17" class="abs medium">12/17</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="4" class="abs medium">2/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="70.58" class="pct medium">70.58%</td>
	<td data-value="17" class="abs medium">12/17</td>
	</tr>

<tr>
	<td class="file medium" data-value="subjects.js"><a href="subjects.js.html">subjects.js</a></td>
	<td data-value="83.05" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 83%"></div><div class="cover-empty" style="width: 17%"></div></div>
	</td>
	<td data-value="83.05" class="pct medium">83.05%</td>
	<td data-value="59" class="abs medium">49/59</td>
	<td data-value="85.71" class="pct medium">85.71%</td>
	<td data-value="14" class="abs medium">12/14</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="83.05" class="pct medium">83.05%</td>
	<td data-value="59" class="abs medium">49/59</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-07T00:58:41.352Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    