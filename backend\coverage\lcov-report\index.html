
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">54.7% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>215/393</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">46.47% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>66/142</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">67.27% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>37/55</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">54.4% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>210/386</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="config"><a href="config/index.html">config</a></td>
	<td data-value="66" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 66%"></div><div class="cover-empty" style="width: 34%"></div></div>
	</td>
	<td data-value="66" class="pct medium">66%</td>
	<td data-value="100" class="abs medium">66/100</td>
	<td data-value="75.51" class="pct medium">75.51%</td>
	<td data-value="49" class="abs medium">37/49</td>
	<td data-value="76.92" class="pct medium">76.92%</td>
	<td data-value="26" class="abs medium">20/26</td>
	<td data-value="65.26" class="pct medium">65.26%</td>
	<td data-value="95" class="abs medium">62/95</td>
	</tr>

<tr>
	<td class="file low" data-value="middleware"><a href="middleware/index.html">middleware</a></td>
	<td data-value="43.39" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 43%"></div><div class="cover-empty" style="width: 57%"></div></div>
	</td>
	<td data-value="43.39" class="pct low">43.39%</td>
	<td data-value="106" class="abs low">46/106</td>
	<td data-value="19.6" class="pct low">19.6%</td>
	<td data-value="51" class="abs low">10/51</td>
	<td data-value="45.45" class="pct low">45.45%</td>
	<td data-value="11" class="abs low">5/11</td>
	<td data-value="43.26" class="pct low">43.26%</td>
	<td data-value="104" class="abs low">45/104</td>
	</tr>

<tr>
	<td class="file low" data-value="models"><a href="models/index.html">models</a></td>
	<td data-value="49.31" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 49%"></div><div class="cover-empty" style="width: 51%"></div></div>
	</td>
	<td data-value="49.31" class="pct low">49.31%</td>
	<td data-value="73" class="abs low">36/73</td>
	<td data-value="27.77" class="pct low">27.77%</td>
	<td data-value="18" class="abs low">5/18</td>
	<td data-value="70" class="pct medium">70%</td>
	<td data-value="10" class="abs medium">7/10</td>
	<td data-value="49.31" class="pct low">49.31%</td>
	<td data-value="73" class="abs low">36/73</td>
	</tr>

<tr>
	<td class="file medium" data-value="routes"><a href="routes/index.html">routes</a></td>
	<td data-value="58.77" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 58%"></div><div class="cover-empty" style="width: 42%"></div></div>
	</td>
	<td data-value="58.77" class="pct medium">58.77%</td>
	<td data-value="114" class="abs medium">67/114</td>
	<td data-value="58.33" class="pct medium">58.33%</td>
	<td data-value="24" class="abs medium">14/24</td>
	<td data-value="62.5" class="pct medium">62.5%</td>
	<td data-value="8" class="abs medium">5/8</td>
	<td data-value="58.77" class="pct medium">58.77%</td>
	<td data-value="114" class="abs medium">67/114</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-07T00:58:41.352Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    