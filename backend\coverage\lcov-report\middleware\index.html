
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for middleware</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> middleware</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">43.39% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>46/106</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">19.6% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>10/51</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">45.45% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>5/11</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">43.26% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>45/104</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="errorHandler.js"><a href="errorHandler.js.html">errorHandler.js</a></td>
	<td data-value="34.88" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 34%"></div><div class="cover-empty" style="width: 66%"></div></div>
	</td>
	<td data-value="34.88" class="pct low">34.88%</td>
	<td data-value="43" class="abs low">15/43</td>
	<td data-value="11.11" class="pct low">11.11%</td>
	<td data-value="36" class="abs low">4/36</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="34.88" class="pct low">34.88%</td>
	<td data-value="43" class="abs low">15/43</td>
	</tr>

<tr>
	<td class="file medium" data-value="logger.js"><a href="logger.js.html">logger.js</a></td>
	<td data-value="87.5" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 87%"></div><div class="cover-empty" style="width: 13%"></div></div>
	</td>
	<td data-value="87.5" class="pct medium">87.5%</td>
	<td data-value="16" class="abs medium">14/16</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="6" class="abs medium">4/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="87.5" class="pct medium">87.5%</td>
	<td data-value="16" class="abs medium">14/16</td>
	</tr>

<tr>
	<td class="file low" data-value="validation.js"><a href="validation.js.html">validation.js</a></td>
	<td data-value="36.17" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 36%"></div><div class="cover-empty" style="width: 64%"></div></div>
	</td>
	<td data-value="36.17" class="pct low">36.17%</td>
	<td data-value="47" class="abs low">17/47</td>
	<td data-value="22.22" class="pct low">22.22%</td>
	<td data-value="9" class="abs low">2/9</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="9" class="abs low">3/9</td>
	<td data-value="35.55" class="pct low">35.55%</td>
	<td data-value="45" class="abs low">16/45</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-07T00:58:41.352Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    