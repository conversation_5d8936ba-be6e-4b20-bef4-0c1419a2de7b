const request = require('supertest');
const app = require('../../src/app');

// 创建测试请求辅助函数
const testRequest = () => request(app.callback());

describe('Subjects API', () => {
  describe('GET /api/v1/subjects', () => {
    it('应该返回空的学科列表 - 初始状态', async () => {
      const response = await testRequest()
        .get('/api/v1/subjects')
        .expect(200);

      // 验证响应格式符合API契约
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('code', 200);
      expect(response.body).toHaveProperty('message', '获取学科列表成功');
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('timestamp');

      // 验证data是数组
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data).toHaveLength(0);
    });

    it('应该返回正确的Content-Type', async () => {
      await testRequest()
        .get('/api/v1/subjects')
        .expect('Content-Type', /json/)
        .expect(200);
    });

    it('应该在合理时间内响应', async () => {
      const startTime = Date.now();

      await testRequest()
        .get('/api/v1/subjects')
        .expect(200);

      const responseTime = Date.now() - startTime;

      // API响应时间应该小于3秒 (契约要求)
      expect(responseTime).toBeLessThan(3000);
    });

    it('timestamp应该是有效的ISO 8601格式', async () => {
      const response = await testRequest()
        .get('/api/v1/subjects')
        .expect(200);

      const timestamp = response.body.timestamp;

      // 验证timestamp格式
      expect(timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);

      // 验证可以解析为有效日期
      expect(new Date(timestamp).getTime()).toBeGreaterThan(0);
    });
  });

  describe('GET /api/v1/subjects/:id', () => {
    it('应该返回400错误 - 无效的ID格式', async () => {
      const response = await testRequest()
        .get('/api/v1/subjects/invalid')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('code', 400);
      expect(response.body).toHaveProperty('message', '请求参数错误');
      expect(response.body).toHaveProperty('error', '学科ID必须是正整数');
      expect(response.body).toHaveProperty('timestamp');
    });

    it('应该返回400错误 - 负数ID', async () => {
      const response = await testRequest()
        .get('/api/v1/subjects/-1')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('code', 400);
      expect(response.body).toHaveProperty('message', '请求参数错误');
      expect(response.body).toHaveProperty('error', '学科ID必须是正整数');
    });

    it('应该返回404错误 - 学科不存在', async () => {
      const response = await testRequest()
        .get('/api/v1/subjects/999')
        .expect(404);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('code', 404);
      expect(response.body).toHaveProperty('message', '学科不存在');
      expect(response.body).toHaveProperty('error', '未找到ID为999的学科');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('POST /api/v1/subjects', () => {
    it('应该成功创建学科', async () => {
      const subjectData = {
        name: '高等数学',
        description: '高等数学复习资料'
      };

      const response = await testRequest()
        .post('/api/v1/subjects')
        .send(subjectData)
        .expect(201);

      // 验证响应格式符合API契约
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('code', 201);
      expect(response.body).toHaveProperty('message', '学科创建成功');
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('timestamp');

      // 验证返回的学科数据
      const createdSubject = response.body.data;
      expect(createdSubject).toHaveProperty('id');
      expect(createdSubject.name).toBe(subjectData.name);
      expect(createdSubject.description).toBe(subjectData.description);
      expect(createdSubject).toHaveProperty('created_at');
      expect(createdSubject).toHaveProperty('updated_at');

      // 验证ID是正整数
      expect(typeof createdSubject.id).toBe('number');
      expect(createdSubject.id).toBeGreaterThan(0);
    });

    it('应该返回400错误 - 缺少学科名称', async () => {
      const response = await testRequest()
        .post('/api/v1/subjects')
        .send({ description: '描述' })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('code', 400);
      expect(response.body).toHaveProperty('message', '请求参数错误');
      expect(response.body.error).toContain('学科名称');
    });

    it('应该返回400错误 - 学科名称为空字符串', async () => {
      const response = await testRequest()
        .post('/api/v1/subjects')
        .send({ name: '', description: '描述' })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('code', 400);
    });

    it('应该返回400错误 - 学科名称过长', async () => {
      const longName = 'a'.repeat(51); // 超过50字符限制

      const response = await testRequest()
        .post('/api/v1/subjects')
        .send({ name: longName, description: '描述' })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('code', 400);
    });

    it('应该返回400错误 - 学科名称重复', async () => {
      const subjectData = {
        name: '重复测试学科',
        description: '用于测试重复名称的学科'
      };

      // 先创建一个学科
      await testRequest()
        .post('/api/v1/subjects')
        .send(subjectData)
        .expect(201);

      // 尝试创建重复名称的学科
      const response = await testRequest()
        .post('/api/v1/subjects')
        .send(subjectData)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('code', 400);
      expect(response.body).toHaveProperty('message', '请求参数错误');
      expect(response.body).toHaveProperty('error', '学科名称已存在');
    });

    it('应该正确处理可选的description字段', async () => {
      const subjectData = {
        name: '概率论'
        // 不提供description
      };

      const response = await testRequest()
        .post('/api/v1/subjects')
        .send(subjectData)
        .expect(201);

      expect(response.body.data.name).toBe(subjectData.name);
      expect(response.body.data.description).toBeUndefined();
    });
  });

  describe('DELETE /api/v1/subjects/:id', () => {
    it('应该返回400错误 - 无效的ID格式', async () => {
      const response = await testRequest()
        .delete('/api/v1/subjects/invalid')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('code', 400);
      expect(response.body).toHaveProperty('message', '请求参数错误');
      expect(response.body).toHaveProperty('error', '学科ID必须是正整数');
    });

    it('应该返回404错误 - 学科不存在', async () => {
      const response = await testRequest()
        .delete('/api/v1/subjects/999')
        .expect(404);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('code', 404);
      expect(response.body).toHaveProperty('message', '学科不存在');
      expect(response.body).toHaveProperty('error', '未找到ID为999的学科');
    });

    it('应该成功删除存在的学科', async () => {
      // 先创建一个学科
      const createResponse = await testRequest()
        .post('/api/v1/subjects')
        .send({ name: '待删除学科', description: '测试删除' })
        .expect(201);

      const subjectId = createResponse.body.data.id;

      // 删除学科
      const deleteResponse = await testRequest()
        .delete(`/api/v1/subjects/${subjectId}`)
        .expect(200);

      expect(deleteResponse.body).toHaveProperty('success', true);
      expect(deleteResponse.body).toHaveProperty('code', 200);
      expect(deleteResponse.body).toHaveProperty('message', '学科删除成功');
      expect(deleteResponse.body).toHaveProperty('data');
      expect(deleteResponse.body).toHaveProperty('timestamp');

      // 验证学科确实被删除了
      await testRequest()
        .get(`/api/v1/subjects/${subjectId}`)
        .expect(404);
    });
  });

  describe('API错误处理', () => {
    it('应该处理无效的JSON请求体', async () => {
      const response = await testRequest()
        .post('/api/v1/subjects')
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('code', 400);
    });

    it('应该处理缺少Content-Type的请求', async () => {
      const response = await testRequest()
        .post('/api/v1/subjects')
        .send({ name: '测试学科' });

      // 应该能正常处理，因为koa-bodyparser会自动处理
      expect([200, 201, 400]).toContain(response.status);
    });
  });

  describe('集成测试 - 完整工作流', () => {
    it('应该支持完整的CRUD操作流程', async () => {
      // 1. 获取初始空列表
      let response = await testRequest()
        .get('/api/v1/subjects')
        .expect(200);
      expect(response.body.data).toHaveLength(0);

      // 2. 创建学科
      const createResponse = await testRequest()
        .post('/api/v1/subjects')
        .send({ name: '数据结构', description: '数据结构与算法' })
        .expect(201);

      const subjectId = createResponse.body.data.id;

      // 3. 获取学科列表，应该有1个学科
      response = await testRequest()
        .get('/api/v1/subjects')
        .expect(200);
      expect(response.body.data).toHaveLength(1);

      // 4. 获取特定学科详情
      response = await testRequest()
        .get(`/api/v1/subjects/${subjectId}`)
        .expect(200);
      expect(response.body.data.name).toBe('数据结构');

      // 5. 删除学科
      await testRequest()
        .delete(`/api/v1/subjects/${subjectId}`)
        .expect(200);

      // 6. 验证学科已被删除
      response = await testRequest()
        .get('/api/v1/subjects')
        .expect(200);
      expect(response.body.data).toHaveLength(0);
    });
  });
});
