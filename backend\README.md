# 期末复习平台 - 后端API

基于Koa.js构建的RESTful API服务，为期末复习平台提供后端支持。

## 技术栈

- **框架**: Koa.js 2.14.2
- **数据库**: SQLite3 5.1.7
- **测试**: Jest 29.7.0 + Supertest 6.3.3
- **开发工具**: Nodemon 3.0.2

## 主要功能

- 🏥 系统健康检查API
- 📚 学科管理API (CRUD操作)
- 📁 文件管理API (计划中)
- 🔍 内容搜索API (计划中)

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

服务将在 http://localhost:3000 启动

### 生产模式

```bash
npm start
```

## 测试

### 运行所有测试

```bash
npm test
```

### 运行特定测试

```bash
npm test -- --testNamePattern="健康检查"
```

### 测试覆盖率

```bash
npm run test:coverage
```

## API文档

### 健康检查

```
GET /api/v1/health
```

返回系统健康状态，包括：
- 数据库连接状态
- 系统运行时间
- 环境信息
- 响应时间

### 学科管理

```
GET    /api/v1/subjects     # 获取所有学科
POST   /api/v1/subjects     # 创建新学科
GET    /api/v1/subjects/:id # 获取指定学科
DELETE /api/v1/subjects/:id # 删除指定学科
```

## 项目结构

```
backend/
├── src/
│   ├── app.js              # 主应用文件
│   ├── config/
│   │   └── database.js     # 数据库配置
│   ├── middleware/
│   │   ├── errorHandler.js # 错误处理中间件
│   │   ├── logger.js       # 日志中间件
│   │   └── validation.js   # 数据验证中间件
│   ├── models/
│   │   └── Subject.js      # 学科数据模型
│   └── routes/
│       ├── health.js       # 健康检查路由
│       ├── subjects.js     # 学科管理路由
│       └── dev.js          # 开发工具路由
├── tests/
│   ├── integration/        # 集成测试
│   └── setup.js           # 测试环境设置
├── docs/
│   └── development/        # 开发文档
└── package.json
```

## 环境变量

```bash
NODE_ENV=development        # 运行环境
PORT=3000                  # 服务端口
DATABASE_PATH=./data.db    # 数据库文件路径
```

## 开发指南

### 添加新的API端点

1. 在 `src/routes/` 目录下创建路由文件
2. 在 `src/app.js` 中注册路由
3. 在 `tests/integration/` 目录下添加测试
4. 更新API文档

### 数据库操作

项目使用SQLite作为数据库，相关操作在 `src/config/database.js` 中定义。

### 错误处理

所有API错误都通过统一的错误处理中间件处理，返回标准格式的错误响应。

## 最近更新

### v1.0.0 (2025-08-05)

- ✅ 修复了CORS中间件兼容性问题
- ✅ 完善了健康检查API测试
- ✅ 优化了测试环境配置
- ✅ 添加了完整的API测试套件

详细的修复记录请查看 `docs/development/test-fix-summary.md`

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 运行测试确保通过
5. 提交Pull Request

## 许可证

MIT License
