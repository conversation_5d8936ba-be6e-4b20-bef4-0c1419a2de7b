
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for config</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> config</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">36.66% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>33/90</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">32.5% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>13/40</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">46.15% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>12/26</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">39.28% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>33/84</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="database.js"><a href="database.js.html">database.js</a></td>
	<td data-value="36.66" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 36%"></div><div class="cover-empty" style="width: 64%"></div></div>
	</td>
	<td data-value="36.66" class="pct low">36.66%</td>
	<td data-value="90" class="abs low">33/90</td>
	<td data-value="32.5" class="pct low">32.5%</td>
	<td data-value="40" class="abs low">13/40</td>
	<td data-value="46.15" class="pct low">46.15%</td>
	<td data-value="26" class="abs low">12/26</td>
	<td data-value="39.28" class="pct low">39.28%</td>
	<td data-value="84" class="abs low">33/84</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-07T00:52:09.549Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    